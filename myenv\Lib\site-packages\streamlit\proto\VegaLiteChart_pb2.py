# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/VegaLiteChart.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import DataFrame_pb2 as streamlit_dot_proto_dot_DataFrame__pb2
from streamlit.proto import NamedDataSet_pb2 as streamlit_dot_proto_dot_NamedDataSet__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#streamlit/proto/VegaLiteChart.proto\x1a\x1fstreamlit/proto/DataFrame.proto\x1a\"streamlit/proto/NamedDataSet.proto\"{\n\rVegaLiteChart\x12\x0c\n\x04spec\x18\x01 \x01(\t\x12\x18\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\n.DataFrame\x12\x1f\n\x08\x64\x61tasets\x18\x04 \x03(\x0b\x32\r.NamedDataSet\x12\x1b\n\x13use_container_width\x18\x05 \x01(\x08J\x04\x08\x03\x10\x04\x42\x32\n\x1c\x63om.snowflake.apps.streamlitB\x12VegaLiteChartProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.VegaLiteChart_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\022VegaLiteChartProto'
  _globals['_VEGALITECHART']._serialized_start=108
  _globals['_VEGALITECHART']._serialized_end=231
# @@protoc_insertion_point(module_scope)
