#!/usr/bin/env python3
"""
Test script to verify all endpoints are working
"""

import requests
import json

def test_root_endpoint():
    """Test the root endpoint"""
    print("🔍 Testing root endpoint (/)...")
    try:
        response = requests.get("http://127.0.0.1:8000/", timeout=10)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
            print("✅ Root endpoint working!")
        else:
            print(f"❌ Root endpoint failed: {response.text}")
    except Exception as e:
        print(f"❌ Root endpoint error: {e}")
    print()

def test_api_endpoint():
    """Test the API endpoint"""
    print("🔍 Testing API endpoint (/api/generate)...")
    try:
        data = {"user_question": "What are your operating hours?"}
        response = requests.post(
            "http://127.0.0.1:8000/api/generate", 
            json=data, 
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            print("✅ API endpoint working!")
        else:
            print(f"❌ API endpoint failed: {response.text}")
    except Exception as e:
        print(f"❌ API endpoint error: {e}")
    print()

def test_test_page():
    """Test the test page endpoint"""
    print("🔍 Testing test page endpoint (/test)...")
    try:
        response = requests.get("http://127.0.0.1:8000/test", timeout=10)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print(f"Content length: {len(response.text)} characters")
            print("✅ Test page endpoint working!")
            print("🌐 You can open http://127.0.0.1:8000/test in your browser to test the chatbot!")
        else:
            print(f"❌ Test page failed: {response.text}")
    except Exception as e:
        print(f"❌ Test page error: {e}")
    print()

if __name__ == "__main__":
    print("🧪 Testing Yellow Penguin Chatbot Endpoints\n")
    test_root_endpoint()
    test_api_endpoint()
    test_test_page()
    print("🎉 All tests completed!")
