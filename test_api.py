#!/usr/bin/env python3
"""
Simple test script to verify the chatbot API is working
"""

import requests
import json

def test_api():
    # API endpoint
    url = "http://127.0.0.1:8000/api/generate"
    
    # Test data
    test_data = {
        "user_question": "Hello! What is Yellow Penguin?"
    }
    
    # Headers
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print("Testing chatbot API...")
        print(f"URL: {url}")
        print(f"Request: {json.dumps(test_data, indent=2)}")
        
        # Make the request
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"\nResponse Data: {json.dumps(response_data, indent=2)}")
            print("\n✅ API test successful!")
        else:
            print(f"\n❌ API test failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"\n❌ Request failed: {e}")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")

if __name__ == "__main__":
    test_api()
